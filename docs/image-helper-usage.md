# 图片处理工具使用指南

## 概述

`src/utils/imageHelper.js` 提供了一套完整的图片处理工具，专门解决项目中的图片加载、base64转换和显示问题。

## 主要功能

### 1. 图片格式检测

```javascript
import { detectImageFormat } from '@/utils/imageHelper'

// 检测base64图片格式
const format1 = detectImageFormat('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...')
console.log(format1) // 'png'

// 检测URL图片格式
const format2 = detectImageFormat('/assets/logo.jpg')
console.log(format2) // 'jpeg'
```

### 2. Base64图片验证和修复

```javascript
import { validateBase64Image, fixBase64Image } from '@/utils/imageHelper'

// 验证base64图片数据
const isValid = validateBase64Image('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...')
console.log(isValid) // true/false

// 修复损坏的base64数据
const brokenImage = 'data:,iVBORw0KGgoAAAANSUhEUgAA...' // 缺少MIME类型
const fixedImage = fixBase64Image(brokenImage, 'png')
console.log(fixedImage) // 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'
```

### 3. 单个图片预加载

```javascript
import { preloadImage } from '@/utils/imageHelper'

// 预加载单个图片
preloadImage('/assets/logo.png', {
  timeout: 5000,
  retries: 2
})
.then(result => {
  console.log('图片加载成功:', result)
  // result: { src, width, height, format }
})
.catch(error => {
  console.error('图片加载失败:', error)
})
```

### 4. 批量图片预加载

```javascript
import { preloadImages } from '@/utils/imageHelper'

const imageSources = [
  require('@/assets/logo.png'),
  require('@/assets/banner.jpg'),
  'https://example.com/image.webp'
]

preloadImages(imageSources, {
  concurrency: 3, // 并发数
  timeout: 5000,
  onProgress: (completed, total, result) => {
    console.log(`加载进度: ${completed}/${total}`)
    console.log('当前加载:', result)
  }
})
.then(results => {
  console.log('所有图片加载完成:', results)
})
.catch(error => {
  console.error('批量加载失败:', error)
})
```

### 5. 获取图片详细信息

```javascript
import { getImageInfo } from '@/utils/imageHelper'

getImageInfo('/assets/logo.png')
.then(info => {
  console.log('图片信息:', info)
  // {
  //   src: '/assets/logo.png',
  //   width: 200,
  //   height: 100,
  //   format: 'png',
  //   size: 15360, // 字节
  //   isBase64: false,
  //   isValid: true
  // }
})
```

### 6. 创建优化的图片元素

```javascript
import { createImageElement } from '@/utils/imageHelper'

const img = createImageElement('/assets/logo.png', {
  alt: 'Logo',
  class: 'logo-image',
  loading: 'lazy'
})

document.body.appendChild(img)
```

## 在Vue组件中使用

### 组件预加载示例

```vue
<template>
  <div>
    <img v-for="image in loadedImages" 
         :key="image.src" 
         :src="image.src" 
         :alt="image.alt" />
  </div>
</template>

<script>
import { preloadImages } from '@/utils/imageHelper'

export default {
  data() {
    return {
      loadedImages: [],
      imageSources: [
        require('@/assets/product1.png'),
        require('@/assets/product2.png'),
        require('@/assets/product3.png')
      ]
    }
  },
  
  async mounted() {
    try {
      const results = await preloadImages(this.imageSources, {
        concurrency: 2,
        onProgress: (completed, total) => {
          console.log(`预加载进度: ${completed}/${total}`)
        }
      })
      
      this.loadedImages = results.map((result, index) => ({
        src: result.src,
        alt: `Product ${index + 1}`
      }))
      
    } catch (error) {
      console.error('图片预加载失败:', error)
    }
  }
}
</script>
```

### 图片验证混入

```javascript
// mixins/imageValidation.js
import { validateBase64Image, fixBase64Image } from '@/utils/imageHelper'

export default {
  methods: {
    validateAndFixImage(src) {
      if (!src) return ''
      
      if (src.startsWith('data:image/')) {
        if (!validateBase64Image(src)) {
          console.warn('检测到损坏的base64图片，尝试修复...')
          return fixBase64Image(src)
        }
      }
      
      return src
    }
  }
}
```

## 最佳实践

### 1. 错误处理

```javascript
import { preloadImage } from '@/utils/imageHelper'

async function loadImageSafely(src) {
  try {
    const result = await preloadImage(src, {
      timeout: 3000,
      retries: 1
    })
    return result
  } catch (error) {
    console.warn(`图片加载失败，使用默认图片: ${src}`)
    return { src: '/assets/default-image.png' }
  }
}
```

### 2. 性能优化

```javascript
// 在路由切换时预加载下一页面的图片
router.beforeEach(async (to, from, next) => {
  if (to.meta.preloadImages) {
    try {
      await preloadImages(to.meta.preloadImages, {
        concurrency: 2,
        timeout: 2000
      })
    } catch (error) {
      console.warn('页面图片预加载失败:', error)
    }
  }
  next()
})
```

### 3. 图片大小优化

```javascript
// 检查大图片文件
import { getImageInfo } from '@/utils/imageHelper'

async function checkImageSize(src) {
  const info = await getImageInfo(src)
  
  if (info.size > 100 * 1024) { // 大于100KB
    console.warn(`大图片文件: ${src} (${(info.size / 1024).toFixed(2)}KB)`)
    // 可以考虑压缩或使用WebP格式
  }
  
  return info
}
```

## 故障排除

### 常见问题

1. **Base64图片显示不正常**
   - 使用 `validateBase64Image()` 检查数据完整性
   - 使用 `fixBase64Image()` 尝试修复

2. **图片加载超时**
   - 增加 `timeout` 参数
   - 检查网络连接和图片URL

3. **批量加载性能问题**
   - 降低 `concurrency` 参数
   - 分批加载大量图片

4. **内存占用过高**
   - 避免同时加载过多大图片
   - 使用图片懒加载

### 调试技巧

```javascript
// 启用详细日志
import imageHelper from '@/utils/imageHelper'

// 在开发环境启用调试
if (process.env.NODE_ENV === 'development') {
  window.imageHelper = imageHelper
  console.log('图片处理工具已挂载到 window.imageHelper')
}
```

## 更新日志

- **v1.0.0** (2025-07-15): 初始版本，包含所有核心功能
- 修复了项目中的base64图片识别问题
- 解决了图片预加载错误
- 优化了webpack图片处理配置
