/**
 * 图片处理工具类
 * 解决本地图片base64转换和显示问题
 */

/**
 * 检测图片格式
 * @param {string} src - 图片源（可能是base64或URL）
 * @returns {string} 图片格式
 */
export function detectImageFormat(src) {
  if (!src) return 'unknown'
  
  // 检查是否为base64格式
  if (src.startsWith('data:image/')) {
    const match = src.match(/data:image\/([^;]+)/)
    return match ? match[1] : 'unknown'
  }
  
  // 检查文件扩展名
  const extension = src.split('.').pop()?.toLowerCase()
  const formatMap = {
    'jpg': 'jpeg',
    'jpeg': 'jpeg',
    'png': 'png',
    'gif': 'gif',
    'svg': 'svg+xml',
    'webp': 'webp'
  }
  
  return formatMap[extension] || 'unknown'
}

/**
 * 验证base64图片数据是否有效
 * @param {string} base64Data - base64图片数据
 * @returns {boolean} 是否有效
 */
export function validateBase64Image(base64Data) {
  if (!base64Data || typeof base64Data !== 'string') {
    return false
  }
  
  // 检查base64格式
  if (!base64Data.startsWith('data:image/')) {
    return false
  }
  
  try {
    // 尝试解析base64数据
    const base64String = base64Data.split(',')[1]
    if (!base64String) return false
    
    // 检查base64字符串是否有效
    const decoded = atob(base64String)
    return decoded.length > 0
  } catch (error) {
    console.warn('Invalid base64 image data:', error)
    return false
  }
}

/**
 * 修复base64图片数据
 * @param {string} src - 可能损坏的图片源
 * @param {string} fallbackFormat - 备用格式
 * @returns {string} 修复后的图片源
 */
export function fixBase64Image(src, fallbackFormat = 'png') {
  if (!src) return ''
  
  // 如果不是base64，直接返回
  if (!src.includes('data:image/')) {
    return src
  }
  
  try {
    // 检查是否缺少MIME类型
    if (src.startsWith('data:,')) {
      const base64Data = src.replace('data:,', '')
      return `data:image/${fallbackFormat};base64,${base64Data}`
    }
    
    // 检查是否缺少base64标识
    if (src.startsWith('data:image/') && !src.includes('base64,')) {
      const parts = src.split(',')
      if (parts.length === 2) {
        const mimeType = parts[0]
        const data = parts[1]
        return `${mimeType};base64,${data}`
      }
    }
    
    return src
  } catch (error) {
    console.warn('Failed to fix base64 image:', error)
    return src
  }
}

/**
 * 预加载图片
 * @param {string} src - 图片源
 * @param {Object} options - 选项
 * @returns {Promise} 加载Promise
 */
export function preloadImage(src, options = {}) {
  const { timeout = 5000, retries = 2 } = options
  
  return new Promise((resolve, reject) => {
    if (!src) {
      reject(new Error('Image source is required'))
      return
    }
    
    // 验证base64图片
    if (src.startsWith('data:image/') && !validateBase64Image(src)) {
      const fixedSrc = fixBase64Image(src)
      if (!validateBase64Image(fixedSrc)) {
        reject(new Error('Invalid base64 image data'))
        return
      }
      src = fixedSrc
    }
    
    const img = new Image()
    let retryCount = 0
    
    const attemptLoad = () => {
      img.onload = () => {
        resolve({
          src,
          width: img.naturalWidth,
          height: img.naturalHeight,
          format: detectImageFormat(src)
        })
      }
      
      img.onerror = () => {
        retryCount++
        if (retryCount <= retries) {
          console.warn(`Image load failed, retrying (${retryCount}/${retries}):`, src)
          setTimeout(attemptLoad, 1000 * retryCount)
        } else {
          reject(new Error(`Failed to load image after ${retries} retries: ${src}`))
        }
      }
      
      // 设置超时
      setTimeout(() => {
        if (!img.complete) {
          reject(new Error(`Image load timeout: ${src}`))
        }
      }, timeout)
      
      img.src = src
    }
    
    attemptLoad()
  })
}

/**
 * 批量预加载图片
 * @param {Array} sources - 图片源数组
 * @param {Object} options - 选项
 * @returns {Promise} 加载Promise
 */
export function preloadImages(sources, options = {}) {
  const { concurrency = 3, onProgress } = options
  
  return new Promise((resolve, reject) => {
    if (!Array.isArray(sources) || sources.length === 0) {
      resolve([])
      return
    }
    
    const results = []
    const errors = []
    let completed = 0
    let running = 0
    let index = 0
    
    const processNext = () => {
      while (running < concurrency && index < sources.length) {
        const currentIndex = index++
        const src = sources[currentIndex]
        running++
        
        preloadImage(src, options)
          .then(result => {
            results[currentIndex] = result
            completed++
            running--
            
            if (onProgress) {
              onProgress(completed, sources.length, result)
            }
            
            if (completed === sources.length) {
              resolve(results.filter(Boolean))
            } else {
              processNext()
            }
          })
          .catch(error => {
            errors[currentIndex] = error
            completed++
            running--
            
            console.warn(`Failed to preload image ${currentIndex}:`, error)
            
            if (completed === sources.length) {
              if (errors.length === sources.length) {
                reject(new Error('All images failed to load'))
              } else {
                resolve(results.filter(Boolean))
              }
            } else {
              processNext()
            }
          })
      }
    }
    
    processNext()
  })
}

/**
 * 获取图片信息
 * @param {string} src - 图片源
 * @returns {Promise} 图片信息Promise
 */
export function getImageInfo(src) {
  return preloadImage(src).then(result => ({
    ...result,
    size: estimateImageSize(src),
    isBase64: src.startsWith('data:image/'),
    isValid: validateBase64Image(src) || !src.startsWith('data:image/')
  }))
}

/**
 * 估算图片大小（字节）
 * @param {string} src - 图片源
 * @returns {number} 估算大小
 */
function estimateImageSize(src) {
  if (!src.startsWith('data:image/')) {
    return 0 // 无法估算外部图片大小
  }
  
  try {
    const base64String = src.split(',')[1]
    if (!base64String) return 0
    
    // base64编码会增加约33%的大小
    return Math.floor((base64String.length * 3) / 4)
  } catch (error) {
    return 0
  }
}

/**
 * 创建图片元素
 * @param {string} src - 图片源
 * @param {Object} attributes - 图片属性
 * @returns {HTMLImageElement} 图片元素
 */
export function createImageElement(src, attributes = {}) {
  const img = new Image()
  
  // 修复base64图片
  if (src.startsWith('data:image/')) {
    src = fixBase64Image(src)
  }
  
  // 设置属性
  Object.keys(attributes).forEach(key => {
    img.setAttribute(key, attributes[key])
  })
  
  img.src = src
  return img
}

// 默认导出
export default {
  detectImageFormat,
  validateBase64Image,
  fixBase64Image,
  preloadImage,
  preloadImages,
  getImageInfo,
  createImageElement
}
