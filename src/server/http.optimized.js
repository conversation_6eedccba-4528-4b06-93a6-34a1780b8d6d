import axios from 'axios'
import store from '@/store'
import i18n from '@/utils/i18n'
import apiCache, { CachePresets } from '@/utils/api-cache'
import performanceMonitor from '@/utils/performance-monitor'

// 请求队列管理
class RequestQueue {
  constructor() {
    this.queue = new Map()
    this.maxConcurrent = 6 // 最大并发请求数
    this.currentRequests = 0
  }

  async add(key, requestFn) {
    // 如果已有相同请求在进行中，返回该请求的Promise
    if (this.queue.has(key)) {
      return this.queue.get(key)
    }

    const promise = this.executeRequest(requestFn)
    this.queue.set(key, promise)

    try {
      const result = await promise
      return result
    } finally {
      this.queue.delete(key)
    }
  }

  async executeRequest(requestFn) {
    // 等待并发数量限制
    while (this.currentRequests >= this.maxConcurrent) {
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    this.currentRequests++
    try {
      return await requestFn()
    } finally {
      this.currentRequests--
    }
  }
}

const requestQueue = new RequestQueue()

function makeUpCommonParams(params) {
  const isOrderPage = location.pathname.includes('/order')
  const state = store.state
  const userinfo = isOrderPage ? state.orderPage.userinfo : state.userinfo
  const gameinfo = state.gameinfo

  if (!params) params = {}
  params.game_id = +gameinfo.gameId
  params.game_project = gameinfo.gameProject
  params.source = 'web'

  if (state.country) params.country = state.country
  if (state.currency) params.currency = state.currency

  if (!params.openid && userinfo.openid) params.openid = userinfo.openid
  if (!params.uid && userinfo.uid) params.uid = userinfo.uid

  if (!isOrderPage) {
    const localOpenid = localStorage.getItem('openid') || ''
    if (!params.openid && localOpenid) {
      params.openid = localOpenid
    }
  }

  return params
}

function transformUrl(url) {
  // 空值检查
  if (!url || typeof url !== 'string') {
    console.warn('transformUrl: Invalid URL parameter:', url)
    return url || ''
  }

  // 更精确的协议检查
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  const state = store.state

  // 状态检查
  if (!state || !state.gameinfo) {
    console.warn('transformUrl: gameinfo not available, returning original URL')
    return url
  }

  const gameinfo = state.gameinfo

  // URL拼接辅助函数，避免双斜杠问题
  const joinUrl = (host, path) => {
    if (!host) {
      console.warn('transformUrl: Host is undefined, using original URL')
      return path
    }
    const cleanHost = host.replace(/\/+$/, '') // 移除末尾的斜杠
    const cleanPath = path.replace(/^\/+/, '/') // 确保路径以单个斜杠开头
    return cleanHost + cleanPath
  }

  // 特殊处理 /ameCommon - 直接使用 ameHost
  if (url.startsWith('/ameCommon')) {
    return joinUrl(gameinfo.ameHost, url.replace('/ameCommon', '/ame'))
  }

  if (url.startsWith('/token')) {
    return joinUrl(gameinfo.tokenHost, url.replace('/token', ''))
  }
  if (url.startsWith('/account')) {
    return joinUrl(gameinfo.accountHost, url.replace('/account', ''))
  }
  if (url.startsWith('/api')) {
    return joinUrl(gameinfo.apiHost, url.replace('/api', ''))
  }
  if (url.startsWith('/ame')) {
    return joinUrl(gameinfo.ameHost, url.replace('/ame', ''))
  }

  // 默认使用 apiHost，但添加警告
  if (!gameinfo.apiHost) {
    console.warn('transformUrl: apiHost not configured, returning original URL')
    return url
  }

  return joinUrl(gameinfo.apiHost, url)
}

// 创建axios实例
const service = axios.create({
  timeout: 15000, // 增加超时时间
  // 移除不安全的Accept-Encoding请求头，浏览器会自动处理
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const startTime = Date.now()
    config.metadata = { startTime }

    if (config.method === 'post') {
      config.data = makeUpCommonParams(config.data)
    } else {
      config.params = makeUpCommonParams(config.params)
    }

    config.url = transformUrl(config.url)

    const localCountry = sessionStorage.getItem('localCountry') || store.state.urlParams.localCountry
    if (localCountry) {
      const contact = config.url.includes('?') ? '&' : '?'
      config.url += `${contact}country=${localCountry}`
    }

    const localAccountEnv = sessionStorage.getItem('localAccountEnv') || store.state.urlParams.localAccountEnv
    if (localAccountEnv) {
      const prefix = config.url.includes('?') ? '&' : '?'
      config.url += (prefix + `gameServerEnv=${localAccountEnv}`)
    }

    if (config.url.includes('/account/store/user')) {
      const { uid, openid } = config.data || config.params || {}
      if (openid && uid && config.data) {
        delete config.data.openid
      }
    }

    return config
  },
  error => {
    console.error('Request interceptor error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const { config } = response
    const endTime = Date.now()
    const duration = endTime - config.metadata.startTime

    // 记录API性能指标
    performanceMonitor.recordAPIMetric(
      config.url,
      duration,
      response.status,
      {
        method: config.method,
        size: JSON.stringify(response.data).length
      }
    )

    return response.data
  },
  error => {
    const { config, response } = error

    if (config && config.metadata) {
      const endTime = Date.now()
      const duration = endTime - config.metadata.startTime

      // 记录失败的API调用
      performanceMonitor.recordAPIMetric(
        config.url,
        duration,
        response?.status || 0,
        {
          method: config.method,
          error: error.message
        }
      )
    }

    // 错误处理
    if (response) {
      switch (response.status) {
        case 401:
          // 未授权，清除用户信息
          store.dispatch('user/logout')
          break
        case 403:
          console.warn('Access forbidden')
          break
        case 429:
          // 请求过于频繁
          console.warn('Too many requests')
          break
        case 500:
        case 502:
        case 503:
        case 504:
          console.error('Server error:', response.status)
          break
        default:
          console.error('Request failed:', response.status)
      }
    } else if (error.code === 'ECONNABORTED') {
      console.error('Request timeout')
    } else {
      console.error('Network error:', error.message)
    }

    return Promise.reject(error)
  }
)

// 重试机制
async function requestWithRetry(requestFn, maxRetries = 3, delay = 1000) {
  let lastError

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error

      // 如果是客户端错误（4xx），不重试
      if (error.response && error.response.status >= 400 && error.response.status < 500) {
        throw error
      }

      // 最后一次重试失败
      if (i === maxRetries) {
        throw error
      }

      // 指数退避延迟
      const retryDelay = delay * Math.pow(2, i)
      await new Promise(resolve => setTimeout(resolve, retryDelay))
    }
  }

  throw lastError
}

// 优化后的请求控制器
async function requestController(method, url, payload, options = {}) {
  const {
    cache = false,
    cacheOptions = {},
    retry = false,
    maxRetries = 3,
    priority = 'normal'
  } = options

  // 生成请求键用于去重
  const requestKey = `${method}:${url}:${JSON.stringify(payload)}`

  // 如果启用缓存，先尝试从缓存获取
  if (cache) {
    const cached = apiCache.get(url, payload, cacheOptions)
    if (cached) {
      return cached
    }
  }

  const requestFn = async () => {
    const requestConfig = { method, url }
    if (method === 'get') {
      requestConfig.params = payload
    } else {
      requestConfig.data = payload
    }

    const response = await service(requestConfig)

    // 缓存响应
    if (cache && response) {
      apiCache.set(url, payload, response, cacheOptions)
    }

    return response
  }

  // 使用请求队列管理并发
  const queuedRequest = () => requestQueue.add(requestKey, requestFn)

  // 根据是否需要重试选择执行方式
  if (retry) {
    return requestWithRetry(queuedRequest, maxRetries)
  } else {
    return queuedRequest()
  }
}

// 并行请求工具
async function parallelRequests(requests) {
  const promises = requests.map(({ method, url, payload, options }) =>
    requestController(method, url, payload, options)
      .catch(error => ({ error, url }))
  )

  return Promise.allSettled(promises)
}

// 优化后的GET请求
const get = async (url, params, options = {}) => {
  return requestController('get', url, params, {
    cache: true,
    cacheOptions: CachePresets.PRODUCT_LIST,
    retry: true,
    ...options
  })
}

// 优化后的POST请求
const post = async (url, data, options = {}) => {
  return requestController('post', url, data, {
    retry: false, // POST请求通常不重试
    ...options
  })
}

// 预加载关键数据
async function preloadCriticalData() {
  const criticalRequests = [
    {
      method: 'get',
      url: '/api/getIpCurrency',
      payload: { try: 1 },
      options: {
        cache: true,
        cacheOptions: CachePresets.CURRENCY_RATE
      }
    },
    {
      method: 'post',
      url: '/token/channel/list',
      payload: {},
      options: {
        cache: true,
        cacheOptions: CachePresets.PAYMENT_CHANNELS
      }
    }
  ]

  return parallelRequests(criticalRequests)
}

// 清理缓存
function clearCache() {
  apiCache.clear()
}

// 获取缓存统计
function getCacheStats() {
  return apiCache.getStats()
}

export {
  get,
  post,
  makeUpCommonParams,
  service,
  parallelRequests,
  preloadCriticalData,
  clearCache,
  getCacheStats,
  requestController
}
