import { preloadImages, validateBase64Image, fixBase64Image } from '@/utils/imageHelper'

const images = [
    // 'https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-unique/dev/coupon-item.svg',
    // 'https://kg-web-cdn.akamaized.net/prod/versionAdmin/source/web-pay-unique/dev/coupon-item-chosen.svg'
    require('@/assets/common/coupon/coupon-item.png'),
    require('@/assets/common/coupon/coupon-item-chosen.png')
]

const loadImage = (src) => {
    if (!src) return Promise.resolve()

    return new Promise((resolve, reject) => {
        // 修复base64图片数据
        if (src.startsWith('data:image/')) {
            if (!validateBase64Image(src)) {
                src = fixBase64Image(src)
                if (!validateBase64Image(src)) {
                    console.error('Invalid base64 image:', src.substring(0, 50) + '...')
                    reject(new Error('Invalid base64 image data'))
                    return
                }
            }
        }

        // 使用Image对象预加载，更可靠
        const img = new Image()

        img.onload = () => {
            console.log('Image preloaded successfully:', src.substring(0, 50) + '...')
            resolve(img)
        }

        img.onerror = (error) => {
            console.error('Image preload failed:', src.substring(0, 50) + '...', error)
            reject(error)
        }

        // 设置超时
        setTimeout(() => {
            if (!img.complete) {
                reject(new Error('Image preload timeout'))
            }
        }, 5000)

        img.src = src
    })
}

const loadImages = async () => {
    try {
        // 使用优化的批量预加载
        const results = await preloadImages(images, {
            concurrency: 2, // 限制并发数
            timeout: 5000,
            onProgress: (completed, total) => {
                console.log(`Image preload progress: ${completed}/${total}`)
            }
        })

        console.log(`Successfully preloaded ${results.length} images`)
        return results
    } catch (error) {
        console.error('Failed to preload images:', error)

        // 降级到逐个加载
        const results = []
        for (const src of images) {
            try {
                const result = await loadImage(src)
                results.push(result)
            } catch (error) {
                console.warn('Failed to load image:', src.substring(0, 50) + '...', error)
            }
        }

        return results
    }
}

// 启动预加载
loadImages().then(results => {
    console.log('Image preloading completed:', results.length)
}).catch(error => {
    console.error('Image preloading failed:', error)
})
