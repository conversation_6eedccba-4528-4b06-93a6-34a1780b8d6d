#!/usr/bin/env node

/**
 * 测试图片处理修复效果
 */

const fs = require('fs')
const path = require('path')

class ImageFixTester {
  constructor() {
    this.testResults = []
  }

  log(type, message, details = '') {
    const timestamp = new Date().toISOString()
    const logEntry = { timestamp, type, message, details }
    this.testResults.push(logEntry)
    
    const colors = {
      pass: '\x1b[32m✓\x1b[0m',
      fail: '\x1b[31m✗\x1b[0m',
      warn: '\x1b[33m⚠\x1b[0m',
      info: '\x1b[36mℹ\x1b[0m'
    }
    
    console.log(`${colors[type] || '•'} ${message}${details ? ` (${details})` : ''}`)
  }

  // 测试HTTP配置修复
  testHttpConfigFix() {
    console.log('\n🔧 测试HTTP配置修复...')
    
    const httpFiles = [
      'src/server/http.js',
      'src/server/http.optimized.js'
    ]
    
    httpFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8')
        
        if (content.includes("'Accept-Encoding': 'gzip, deflate, br'")) {
          this.log('fail', `${filePath} 仍包含不安全的Accept-Encoding请求头`)
        } else if (content.includes("'Content-Type': 'application/json'")) {
          this.log('pass', `${filePath} 已移除不安全的Accept-Encoding请求头`)
        } else {
          this.log('warn', `${filePath} 请求头配置可能需要检查`)
        }
      } else {
        this.log('warn', `${filePath} 文件不存在`)
      }
    })
  }

  // 测试图片处理工具
  testImageHelper() {
    console.log('\n🖼️  测试图片处理工具...')
    
    const imageHelperPath = 'src/utils/imageHelper.js'
    
    if (fs.existsSync(imageHelperPath)) {
      const content = fs.readFileSync(imageHelperPath, 'utf8')
      
      const requiredFunctions = [
        'detectImageFormat',
        'validateBase64Image',
        'fixBase64Image',
        'preloadImage',
        'preloadImages'
      ]
      
      requiredFunctions.forEach(funcName => {
        if (content.includes(`export function ${funcName}`) || content.includes(`function ${funcName}`)) {
          this.log('pass', `图片处理函数 ${funcName} 已实现`)
        } else {
          this.log('fail', `图片处理函数 ${funcName} 缺失`)
        }
      })
      
      // 检查base64验证逻辑
      if (content.includes('data:image/') && content.includes('atob')) {
        this.log('pass', 'Base64图片验证逻辑已实现')
      } else {
        this.log('fail', 'Base64图片验证逻辑缺失')
      }
      
    } else {
      this.log('fail', '图片处理工具文件不存在')
    }
  }

  // 测试Preload.js修复
  testPreloadFix() {
    console.log('\n⚡ 测试图片预加载修复...')
    
    const preloadPath = 'src/components/product/Preload.js'
    
    if (fs.existsSync(preloadPath)) {
      const content = fs.readFileSync(preloadPath, 'utf8')
      
      if (content.includes('import { preloadImages')) {
        this.log('pass', 'Preload.js 已导入优化的图片处理工具')
      } else {
        this.log('fail', 'Preload.js 未导入图片处理工具')
      }
      
      if (content.includes('validateBase64Image')) {
        this.log('pass', 'Preload.js 已添加base64验证')
      } else {
        this.log('fail', 'Preload.js 缺少base64验证')
      }
      
      if (content.includes('Image()')) {
        this.log('pass', 'Preload.js 使用Image对象预加载')
      } else {
        this.log('warn', 'Preload.js 可能仍使用link标签预加载')
      }
      
    } else {
      this.log('fail', 'Preload.js 文件不存在')
    }
  }

  // 测试webpack配置
  testWebpackConfig() {
    console.log('\n⚙️  测试Webpack配置...')
    
    const configPath = 'vue.config.js'
    
    if (fs.existsSync(configPath)) {
      const content = fs.readFileSync(configPath, 'utf8')
      
      if (content.includes('mimetype:') && content.includes('encoding: \'base64\'')) {
        this.log('pass', 'Webpack图片处理配置已优化')
      } else {
        this.log('warn', 'Webpack图片处理配置可能需要进一步优化')
      }
      
      if (content.includes('fallback:')) {
        this.log('pass', 'Webpack已配置fallback处理')
      } else {
        this.log('warn', 'Webpack缺少fallback配置')
      }
      
      if (content.includes('url-loader')) {
        this.log('pass', 'url-loader配置存在')
      } else {
        this.log('fail', 'url-loader配置缺失')
      }
      
    } else {
      this.log('fail', 'vue.config.js 文件不存在')
    }
  }

  // 检查项目中的图片文件
  checkImageFiles() {
    console.log('\n📁 检查项目图片文件...')
    
    const assetsDirs = [
      'src/assets/common/coupon',
      'src/assets/foundation',
      'src/assets/koa'
    ]
    
    let totalImages = 0
    
    assetsDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        const files = this.getImageFiles(dir)
        totalImages += files.length
        
        if (files.length > 0) {
          this.log('info', `${dir} 包含 ${files.length} 个图片文件`)
          
          // 检查大文件
          files.forEach(file => {
            const stats = fs.statSync(file)
            const sizeKB = (stats.size / 1024).toFixed(2)
            
            if (stats.size > 8 * 1024) { // 大于8KB
              this.log('info', `大图片文件: ${path.basename(file)} (${sizeKB}KB)`)
            }
          })
        } else {
          this.log('warn', `${dir} 目录为空或不包含图片`)
        }
      } else {
        this.log('warn', `${dir} 目录不存在`)
      }
    })
    
    this.log('info', `项目总计包含 ${totalImages} 个图片文件`)
  }

  getImageFiles(dir) {
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp']
    const files = []
    
    function traverse(currentDir) {
      try {
        const items = fs.readdirSync(currentDir)
        
        items.forEach(item => {
          const fullPath = path.join(currentDir, item)
          const stats = fs.statSync(fullPath)
          
          if (stats.isDirectory()) {
            traverse(fullPath)
          } else if (imageExtensions.some(ext => item.toLowerCase().endsWith(ext))) {
            files.push(fullPath)
          }
        })
      } catch (error) {
        // 忽略权限错误等
      }
    }
    
    traverse(dir)
    return files
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📊 测试报告摘要:')
    
    const summary = {
      pass: this.testResults.filter(r => r.type === 'pass').length,
      fail: this.testResults.filter(r => r.type === 'fail').length,
      warn: this.testResults.filter(r => r.type === 'warn').length,
      info: this.testResults.filter(r => r.type === 'info').length
    }
    
    console.log(`✅ 通过: ${summary.pass}`)
    console.log(`❌ 失败: ${summary.fail}`)
    console.log(`⚠️  警告: ${summary.warn}`)
    console.log(`ℹ️  信息: ${summary.info}`)
    
    const successRate = ((summary.pass / (summary.pass + summary.fail)) * 100).toFixed(1)
    console.log(`\n成功率: ${successRate}%`)
    
    if (summary.fail > 0) {
      console.log('\n❌ 需要修复的问题:')
      this.testResults
        .filter(r => r.type === 'fail')
        .forEach(r => console.log(`  • ${r.message}`))
    }
    
    if (summary.warn > 0) {
      console.log('\n⚠️  需要注意的警告:')
      this.testResults
        .filter(r => r.type === 'warn')
        .forEach(r => console.log(`  • ${r.message}`))
    }
  }

  // 运行所有测试
  runAllTests() {
    console.log('🧪 开始测试图片处理修复效果...\n')
    
    this.testHttpConfigFix()
    this.testImageHelper()
    this.testPreloadFix()
    this.testWebpackConfig()
    this.checkImageFiles()
    this.generateReport()
    
    console.log('\n✨ 测试完成!')
  }
}

// 运行测试
const tester = new ImageFixTester()
tester.runAllTests()
